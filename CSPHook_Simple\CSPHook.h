#pragma once

#include <windows.h>
#include <string>
#include <vector>

// CSPHook 导出宏
#ifdef CSPHOOKSIMPLE_EXPORTS
#define CSPHOOK_API __declspec(dllexport)
#else
#define CSPHOOK_API __declspec(dllimport)
#endif

// Hook信息结构
struct HookInfo
{
    void* targetAddress;
    std::vector<uint8_t> originalBytes;
    std::vector<uint8_t> patchBytes;
    bool isApplied;

    HookInfo() : targetAddress(nullptr), isApplied(false) {}
};

// CSPHook 主要功能类
class CSPHOOK_API CSPHook
{
public:
    CSPHook();
    ~CSPHook();

    // 初始化Hook
    bool Initialize();

    // 清理Hook
    void Cleanup();

    // 检查是否已初始化
    bool IsInitialized() const;

private:
    bool m_bInitialized;
    HMODULE m_hTargetModule;
    uintptr_t m_baseAddress;
    std::vector<HookInfo> m_hooks;

    // 内部Hook实现
    bool InstallHooks();
    void RemoveHooks();

    // 获取目标模块基址
    bool GetTargetModuleInfo();

    // 代码补丁功能
    bool CodePatch(void* address, const uint8_t* patchData, size_t patchSize);
    bool RestorePatch(HookInfo& hookInfo);

    // 具体的Hook实现
    bool Hook_SkipSelectWindow();
    bool Hook_HideTrialString();

    // Hook回调函数
    static void __stdcall Hook_Skip_SelectWindow_Callback();
};

// C风格导出函数
extern "C" {
    CSPHOOK_API bool CSPHook_Initialize();
    CSPHOOK_API void CSPHook_Cleanup();
    CSPHOOK_API bool CSPHook_IsInitialized();
}
