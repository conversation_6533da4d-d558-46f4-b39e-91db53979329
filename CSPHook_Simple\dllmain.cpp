#include <windows.h>
#include "CSPHook.h"

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // DLL被加载时
        OutputDebugStringA("CSPHook: DLL_PROCESS_ATTACH\n");
        // 可以选择在这里自动初始化Hook
        // CSPHook_Initialize();
        break;
        
    case DLL_THREAD_ATTACH:
        // 新线程创建时
        break;
        
    case DLL_THREAD_DETACH:
        // 线程结束时
        break;
        
    case DLL_PROCESS_DETACH:
        // DLL被卸载时
        OutputDebugStringA("CSPHook: DLL_PROCESS_DETACH\n");
        // 清理Hook
        CSPHook_Cleanup();
        break;
    }
    return TRUE;
}
