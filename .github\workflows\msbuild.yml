name: Build CSPMOD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest

    strategy:
      matrix:
        configuration: [Release]
        platform: [x64]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: true

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v2

    - name: Install vcpkg and DuiLib
      run: |
        git clone https://github.com/microsoft/vcpkg.git
        .\vcpkg\bootstrap-vcpkg.bat
        .\vcpkg\vcpkg install duilib --triplet ${{ matrix.platform }}-windows
        .\vcpkg\vcpkg integrate install

    - name: Restore NuGet packages
      run: nuget restore CSPMOD.sln

    - name: Prepare udis86 (手动集成)
      run: |
        if (!(Test-Path "third_party/libudis86")) {
          git clone https://github.com/vmt/udis86.git third_party/libudis86
        }
      shell: pwsh

    - name: Build Solution
      run: msbuild CSPMOD.sln /p:Configuration=${{ matrix.configuration }} /p:Platform=${{ matrix.platform }} /p:VcpkgTriplet=${{ matrix.platform }}-windows /p:VcpkgRoot=${{ github.workspace }}\vcpkg

    - name: Upload Artifacts
      if: success()
      uses: actions/upload-artifact@v4
      with:
        name: CSPMOD-${{ matrix.configuration }}-${{ matrix.platform }}
        path: |
          **/$(Configuration)/
          **/$(Platform)/
