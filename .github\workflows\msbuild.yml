name: Build CSPMOD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest

    strategy:
      matrix:
        configuration: [Release]
        platform: [x64]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: true

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v2

    - name: Install vcpkg
      run: |
        git clone https://github.com/microsoft/vcpkg.git
        .\vcpkg\bootstrap-vcpkg.bat
        .\vcpkg\vcpkg integrate install

    - name: Install DuiLib from GitHub
      run: |
        if (!(Test-Path "third_party")) {
          New-Item -ItemType Directory -Path "third_party"
        }
        if (!(Test-Path "third_party/duilib")) {
          git clone https://github.com/duilib/duilib.git third_party/duilib
        }
      shell: pwsh

    - name: Restore NuGet packages
      run: nuget restore CSPMOD.sln

    - name: Prepare udis86 (手动集成)
      run: |
        if (!(Test-Path "third_party/libudis86")) {
          git clone https://github.com/vmt/udis86.git third_party/libudis86
        }
      shell: pwsh

    - name: Build Solution
      run: msbuild CSPMOD.sln /p:Configuration=${{ matrix.configuration }} /p:Platform=${{ matrix.platform }} /p:VcpkgTriplet=${{ matrix.platform }}-windows /p:VcpkgRoot=${{ github.workspace }}\vcpkg

    - name: Upload Artifacts
      if: success()
      uses: actions/upload-artifact@v4
      with:
        name: CSPMOD-${{ matrix.configuration }}-${{ matrix.platform }}
        path: |
          **/$(Configuration)/
          **/$(Platform)/
