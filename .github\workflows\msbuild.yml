name: Build CSPMOD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest
    strategy:
      matrix:
        configuration: [Release]
        platform: [x64]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: true
    
    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v2
    
    - name: Cache nim_duilib
      id: cache-nim-duilib
      uses: actions/cache@v3
      with:
        path: third_party/nim_duilib
        key: ${{ runner.os }}-nim-duilib-${{ hashFiles('**/nim-duilib-version') }}
    
    - name: Prepare nim_duilib
      if: steps.cache-nim-duilib.outputs.cache-hit != 'true'
      run: |
        if (!(Test-Path "third_party")) {
          New-Item -ItemType Directory -Path "third_party"
        }
        
        # 克隆nim_duilib项目
        git clone https://github.com/netease-im/NIM_Duilib_Framework.git third_party/nim_duilib
        
        # 检查项目结构并查找duilib.h
        Write-Host "nim_duilib项目结构："
        Get-ChildItem third_party/nim_duilib -Recurse -Directory | Select-Object -First 10
        Write-Host "查找duilib.h文件："
        Get-ChildItem third_party/nim_duilib -Recurse -Filter "*.h" | Where-Object { $_.Name -like "*duilib*" } | Select-Object FullName
        Write-Host "检查duilib目录内容："
        if (Test-Path "third_party/nim_duilib/duilib") {
          Get-ChildItem third_party/nim_duilib/duilib -Filter "*.h" | Select-Object Name
        }
        Write-Host "查找所有.h文件（前20个）："
        Get-ChildItem third_party/nim_duilib -Recurse -Filter "*.h" | Select-Object -First 20 | Select-Object FullName
      shell: pwsh
    
    - name: Restore NuGet packages
      run: nuget restore CSPMOD.sln
    
    - name: Build CSPMOD_403 Project Only
      run: |
        $nimDuilibPath = Join-Path $env:GITHUB_WORKSPACE "third_party\nim_duilib"
        msbuild CSPMOD_403\CSPMOD_403.vcxproj /p:Configuration=${{ matrix.configuration }} /p:Platform=${{ matrix.platform }} /p:NimDuilibPath=$nimDuilibPath
      shell: pwsh
    
    - name: Upload Artifacts
      if: success()
      uses: actions/upload-artifact@v4
      with:
        name: CSPMOD_403-${{ matrix.configuration }}-${{ matrix.platform }}
        path: |
          CSPMOD_403/x64/Release/
          CSPMOD_403/Release/
          **/*.dll
