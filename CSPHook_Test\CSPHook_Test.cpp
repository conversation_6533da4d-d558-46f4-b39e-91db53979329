#include <windows.h>
#include <iostream>
#include <string>

// 函数指针类型定义
typedef bool (*CSPHook_Initialize_t)();
typedef void (*CSPHook_Cleanup_t)();
typedef bool (*CSPHook_IsInitialized_t)();

int main()
{
    std::wcout << L"CSPHook Test Program" << std::endl;
    std::wcout << L"===================" << std::endl;

    // 加载DLL
    HMODULE hModule = LoadLibrary(L"CSPHook_Simple.dll");
    if (!hModule)
    {
        std::wcout << L"Failed to load CSPHook_Simple.dll" << std::endl;
        std::wcout << L"Error code: " << GetLastError() << std::endl;
        return 1;
    }

    std::wcout << L"DLL loaded successfully" << std::endl;

    // 获取函数指针
    CSPHook_Initialize_t CSPHook_Initialize = 
        (CSPHook_Initialize_t)GetProcAddress(hModule, "CSPHook_Initialize");
    CSPHook_Cleanup_t CSPHook_Cleanup = 
        (CSPHook_Cleanup_t)GetProcAddress(hModule, "CSPHook_Cleanup");
    CSPHook_IsInitialized_t CSPHook_IsInitialized = 
        (CSPHook_IsInitialized_t)GetProcAddress(hModule, "CSPHook_IsInitialized");

    if (!CSPHook_Initialize || !CSPHook_Cleanup || !CSPHook_IsInitialized)
    {
        std::wcout << L"Failed to get function addresses" << std::endl;
        FreeLibrary(hModule);
        return 1;
    }

    std::wcout << L"Function addresses obtained" << std::endl;

    // 测试Hook功能
    std::wcout << L"\nTesting Hook functionality:" << std::endl;

    // 检查初始状态
    bool isInitialized = CSPHook_IsInitialized();
    std::wcout << L"Initial state: " << (isInitialized ? L"Initialized" : L"Not initialized") << std::endl;

    // 初始化Hook
    std::wcout << L"Initializing hooks..." << std::endl;
    bool initResult = CSPHook_Initialize();
    std::wcout << L"Initialize result: " << (initResult ? L"Success" : L"Failed") << std::endl;

    // 检查初始化后状态
    isInitialized = CSPHook_IsInitialized();
    std::wcout << L"After init state: " << (isInitialized ? L"Initialized" : L"Not initialized") << std::endl;

    if (initResult)
    {
        std::wcout << L"\nHooks are now active!" << std::endl;
        std::wcout << L"The following modifications are applied:" << std::endl;
        std::wcout << L"1. Skip initial version selection window" << std::endl;
        std::wcout << L"2. Hide trial version text" << std::endl;
        
        std::wcout << L"\nPress Enter to cleanup and exit..." << std::endl;
        std::cin.get();

        // 清理Hook
        std::wcout << L"Cleaning up hooks..." << std::endl;
        CSPHook_Cleanup();
        
        // 检查清理后状态
        isInitialized = CSPHook_IsInitialized();
        std::wcout << L"After cleanup state: " << (isInitialized ? L"Initialized" : L"Not initialized") << std::endl;
    }

    // 卸载DLL
    FreeLibrary(hModule);
    std::wcout << L"DLL unloaded" << std::endl;

    return 0;
}
