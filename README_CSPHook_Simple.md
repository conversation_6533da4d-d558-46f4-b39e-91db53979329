# CSPHook Simple

一个简化的CSPHook项目，专注于Hook功能的实现。

## 项目结构

```
CSPHook_Simple/
├── CSPHook_Simple.sln          # Visual Studio解决方案文件
├── CSPHook_Simple/
│   ├── CSPHook_Simple.vcxproj  # 项目文件
│   ├── CSPHook.h               # CSPHook头文件
│   ├── CSPHook.cpp             # CSPHook实现
│   └── dllmain.cpp             # DLL入口点
└── .github/workflows/
    └── build-simple.yml        # GitHub Actions构建配置
```

## 功能特性

- **简洁设计**：移除了所有UI依赖，专注于Hook功能
- **C++/C接口**：提供C++类和C风格导出函数
- **跨平台构建**：支持Debug和Release配置
- **自动化构建**：GitHub Actions自动构建和发布

## Hook功能

### 1. 跳过初始选择版本窗口
- **目标地址**: `0x1402B2C18`
- **修改内容**: 将指令改为 `call 0x1434DA340`
- **字节码**: `E8 23 77 22 03`
- **功能**: 跳过软件启动时的版本选择对话框

### 2. 隐藏体验版文字
- **目标地址**: `0x1403D00FC`
- **修改内容**: 将指令改为 `jmp 1403D0155`
- **字节码**: `EB 57`
- **功能**: 隐藏界面上的"体验版：无法使用部分功能"等文字

### 特征码定位
项目使用以下特征码来定位目标函数：

**跳过选择窗口特征码**:
```
4C 8D 9C 24 B0 03 00 00 49 8B 5B 38 49 8B 73 40 49 8B E3 41 5F 41 5E 41 5D 41 5C 5F C3
```

**隐藏体验版文字特征码**:
```
49 8B 87 18 03 00 00 48 89 45 C0 49 8B 87 20 03 00 00 48 89 45 C8 48 85 C0 74 06 F0 44 0F C1 70 08 48 8D 95 48 03 00 00 48 8D 4D C0
```

## API接口

### C++类接口

```cpp
class CSPHook
{
public:
    bool Initialize();      // 初始化Hook
    void Cleanup();         // 清理Hook
    bool IsInitialized();   // 检查初始化状态
};
```

### C风格导出函数

```c
bool CSPHook_Initialize();      // 初始化Hook
void CSPHook_Cleanup();         // 清理Hook
bool CSPHook_IsInitialized();   // 检查初始化状态
```

## 使用方法

### 1. 编译项目

```bash
# 使用MSBuild
msbuild CSPHook_Simple.sln /p:Configuration=Release /p:Platform=x64

# 或使用Visual Studio直接打开CSPHook_Simple.sln
```

### 2. 使用DLL

```cpp
// 加载DLL
HMODULE hModule = LoadLibrary(L"CSPHook_Simple.dll");

// 获取函数指针
typedef bool (*CSPHook_Initialize_t)();
CSPHook_Initialize_t CSPHook_Initialize = 
    (CSPHook_Initialize_t)GetProcAddress(hModule, "CSPHook_Initialize");

// 调用函数
if (CSPHook_Initialize())
{
    // Hook安装成功
}
```

## 开发说明

### 添加Hook逻辑

在 `CSPHook::InstallHooks()` 方法中实现具体的Hook逻辑：

```cpp
bool CSPHook::InstallHooks()
{
    // 1. 找到目标进程/模块
    // 2. 定位目标函数
    // 3. 安装Hook（可使用Microsoft Detours等库）
    
    return true;
}
```

### 推荐的Hook库

- **Microsoft Detours**：微软官方Hook库
- **MinHook**：轻量级Hook库
- **EasyHook**：.NET/Native Hook库

## 构建要求

- Visual Studio 2022 (v143工具集)
- Windows 10 SDK
- C++17标准

## GitHub Actions

项目配置了自动化构建，每次推送代码时会自动：

1. 编译Debug和Release版本
2. 生成构建产物
3. 上传DLL、LIB和PDB文件

## 许可证

[在此添加许可证信息]

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
