#include "CSPHook.h"
#include <iostream>
#include <psapi.h>
#include <cstdint>
#include <cstring>

// 全局实例
static CSPHook* g_pCSPHook = nullptr;

CSPHook::CSPHook() : m_bInitialized(false), m_hTargetModule(nullptr), m_baseAddress(0)
{
}

CSPHook::~CSPHook()
{
    Cleanup();
}

bool CSPHook::Initialize()
{
    if (m_bInitialized)
    {
        return true;
    }

    // 获取目标模块信息
    if (!GetTargetModuleInfo())
    {
        OutputDebugStringA("CSPHook: Failed to get target module info\n");
        return false;
    }

    // 安装Hook
    if (InstallHooks())
    {
        m_bInitialized = true;
        OutputDebugStringA("CSPHook: All hooks installed successfully\n");
        return true;
    }

    return false;
}

void CSPHook::Cleanup()
{
    if (m_bInitialized)
    {
        RemoveHooks();
        m_bInitialized = false;
        OutputDebugStringA("CSPHook: All hooks removed\n");
    }
}

bool CSPHook::IsInitialized() const
{
    return m_bInitialized;
}

bool CSPHook::GetTargetModuleInfo()
{
    // 获取当前进程的主模块（exe文件）
    m_hTargetModule = GetModuleHandle(nullptr);
    if (!m_hTargetModule)
    {
        return false;
    }

    m_baseAddress = reinterpret_cast<uintptr_t>(m_hTargetModule);

    // 获取模块信息用于调试
    MODULEINFO moduleInfo;
    if (GetModuleInformation(GetCurrentProcess(), m_hTargetModule, &moduleInfo, sizeof(moduleInfo)))
    {
        char debugMsg[256];
        sprintf_s(debugMsg, "CSPHook: Target module base: 0x%p, size: 0x%X\n",
                 moduleInfo.lpBaseOfDll, moduleInfo.SizeOfImage);
        OutputDebugStringA(debugMsg);
    }

    return true;
}

bool CSPHook::InstallHooks()
{
    OutputDebugStringA("CSPHook: Installing hooks...\n");

    bool success = true;

    // 1. 跳过初始选择版本的窗口
    if (!Hook_SkipSelectWindow())
    {
        OutputDebugStringA("CSPHook: Failed to install SkipSelectWindow hook\n");
        success = false;
    }

    // 2. 隐藏体验版文字
    if (!Hook_HideTrialString())
    {
        OutputDebugStringA("CSPHook: Failed to install HideTrialString hook\n");
        success = false;
    }

    return success;
}

void CSPHook::RemoveHooks()
{
    OutputDebugStringA("CSPHook: Removing hooks...\n");

    // 恢复所有补丁
    for (auto& hookInfo : m_hooks)
    {
        if (hookInfo.isApplied)
        {
            RestorePatch(hookInfo);
        }
    }

    m_hooks.clear();
}

bool CSPHook::CodePatch(void* address, const uint8_t* patchData, size_t patchSize)
{
    if (!address || !patchData || patchSize == 0)
    {
        return false;
    }

    // 保存原始字节
    HookInfo hookInfo;
    hookInfo.targetAddress = address;
    hookInfo.originalBytes.resize(patchSize);
    hookInfo.patchBytes.assign(patchData, patchData + patchSize);

    // 读取原始字节
    memcpy(hookInfo.originalBytes.data(), address, patchSize);

    // 修改内存保护
    DWORD oldProtect;
    if (!VirtualProtect(address, patchSize, PAGE_EXECUTE_READWRITE, &oldProtect))
    {
        return false;
    }

    // 应用补丁
    memcpy(address, patchData, patchSize);

    // 恢复内存保护
    DWORD temp;
    VirtualProtect(address, patchSize, oldProtect, &temp);

    hookInfo.isApplied = true;
    m_hooks.push_back(hookInfo);

    char debugMsg[256];
    sprintf_s(debugMsg, "CSPHook: Patched %zu bytes at 0x%p\n", patchSize, address);
    OutputDebugStringA(debugMsg);

    return true;
}

bool CSPHook::RestorePatch(HookInfo& hookInfo)
{
    if (!hookInfo.isApplied || !hookInfo.targetAddress)
    {
        return false;
    }

    // 修改内存保护
    DWORD oldProtect;
    if (!VirtualProtect(hookInfo.targetAddress, hookInfo.originalBytes.size(), PAGE_EXECUTE_READWRITE, &oldProtect))
    {
        return false;
    }

    // 恢复原始字节
    memcpy(hookInfo.targetAddress, hookInfo.originalBytes.data(), hookInfo.originalBytes.size());

    // 恢复内存保护
    DWORD temp;
    VirtualProtect(hookInfo.targetAddress, hookInfo.originalBytes.size(), oldProtect, &temp);

    hookInfo.isApplied = false;

    char debugMsg[256];
    sprintf_s(debugMsg, "CSPHook: Restored %zu bytes at 0x%p\n", hookInfo.originalBytes.size(), hookInfo.targetAddress);
    OutputDebugStringA(debugMsg);

    return true;
}

bool CSPHook::Hook_SkipSelectWindow()
{
    // 跳过初始选择版本的窗口
    // 0x1402B2C18处改为 call 0x1434DA340
    // 特征 4C 8D 9C 24 B0 03 00 00 49 8B 5B 38 49 8B 73 40 49 8B E3 41 5F 41 5E 41 5D 41 5C 5F C3

    void* targetAddress = reinterpret_cast<void*>(m_baseAddress + 0x1402B2C18);

    // call 0x1434DA340 的相对地址计算
    // E8 23 77 22 03 (这是相对于下一条指令的偏移)
    uint8_t op_Call_SkipSelectWindow[5] = { 0xE8, 0x23, 0x77, 0x22, 0x03 };

    if (CodePatch(targetAddress, op_Call_SkipSelectWindow, sizeof(op_Call_SkipSelectWindow)))
    {
        OutputDebugStringA("CSPHook: SkipSelectWindow hook installed\n");
        return true;
    }

    return false;
}

bool CSPHook::Hook_HideTrialString()
{
    // 隐藏体验版文字
    // 0x1403D00FC处改为jmp 1403D0155 (相对跳转)
    // 特征49 8B 87 18 03 00 00 48 89 45 C0 49 8B 87 20 03 00 00 48 89 45 C8 48 85 C0 74 06 F0 44 0F C1 70 08 48 8D 95 48 03 00 00 48 8D 4D C0

    void* targetAddress = reinterpret_cast<void*>(m_baseAddress + 0x1403D00FC);

    // EB 57 = jmp short +0x57 (相对跳转)
    uint8_t op_Skip_Trial_String[2] = { 0xEB, 0x57 };

    if (CodePatch(targetAddress, op_Skip_Trial_String, sizeof(op_Skip_Trial_String)))
    {
        OutputDebugStringA("CSPHook: HideTrialString hook installed\n");
        return true;
    }

    return false;
}

// Hook回调函数（如果需要的话）
void __stdcall CSPHook::Hook_Skip_SelectWindow_Callback()
{
    // 这里可以添加自定义的逻辑
    // 比如直接设置为正版验证通过等
    OutputDebugStringA("CSPHook: Skip SelectWindow callback executed\n");
}

// C风格导出函数实现
extern "C" {

    CSPHOOK_API bool CSPHook_Initialize()
    {
        try
        {
            if (!g_pCSPHook)
            {
                g_pCSPHook = new CSPHook();
            }

            return g_pCSPHook->Initialize();
        }
        catch (...)
        {
            OutputDebugStringA("CSPHook: Exception in CSPHook_Initialize\n");
            return false;
        }
    }

    CSPHOOK_API void CSPHook_Cleanup()
    {
        try
        {
            if (g_pCSPHook)
            {
                g_pCSPHook->Cleanup();
                delete g_pCSPHook;
                g_pCSPHook = nullptr;
            }
        }
        catch (...)
        {
            OutputDebugStringA("CSPHook: Exception in CSPHook_Cleanup\n");
        }
    }

    CSPHOOK_API bool CSPHook_IsInitialized()
    {
        try
        {
            if (!g_pCSPHook)
            {
                return false;
            }

            return g_pCSPHook->IsInitialized();
        }
        catch (...)
        {
            OutputDebugStringA("CSPHook: Exception in CSPHook_IsInitialized\n");
            return false;
        }
    }
}
