  msbuild CSPMOD.sln /p:Configuration=Release /p:Platform=x64 /p:VcpkgTriplet=x64-windows /p:VcpkgRoot=D:\a\CSPMOD\CSPMOD\vcpkg
  shell: C:\Program Files\PowerShell\7\pwsh.EXE -command ". '{0}'"
MSBuild version 17.14.14+a129329f1 for .NET Framework
Build started 7/16/2025 2:06:44 PM.

Project "D:\a\CSPMOD\CSPMOD\CSPMOD.sln" on node 1 (default targets).
ValidateSolutionConfiguration:
  Building solution configuration "Release|x64".
Project "D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (1) is building "D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj" (2) on node 1 (default targets).
PrepareForBuild:
  Creating directory "x64\Release\".
  Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
  Creating directory "D:\a\CSPMOD\CSPMOD\x64\Release\".
  Creating directory "x64\Release\CSPMOD_403.tlog\".
InitializeBuildStatus:
  Creating "x64\Release\CSPMOD_403.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
  Touching "x64\Release\CSPMOD_403.tlog\unsuccessfulbuild".
VcpkgTripletSelection:
  Using triplet "x64-windows" from "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64\CL.exe /c /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /ID:\a\CSPMOD\CSPMOD\CSPMOD_403\..\third_party\libudis86 /ID:\a\CSPMOD\CSPMOD\CSPMOD_403\ /ID:\a\CSPMOD\CSPMOD\CSPMOD_403\ /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /Zi /nologo /W3 /WX- /diagnostics:column /sdl /O2 /Oi /GL /D NDEBUG /D CSPMOD403_EXPORTS /D _WINDOWS /D _USRDLL /D ZLIB_STATIC /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR=1 /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MD /GS /Gy /fp:fast /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /permissive /Fo"x64\Release\\" /Fd"x64\Release\vc143.pdb" /external:W3 /Gd /TP /FC /errorReport:queue CSPHook.cpp dllmain.cpp DUI\basic_form.cpp DUI\CatDui.cpp DUI\CatDuiThread.cpp DUI\Dialog\TimeLapseExport_Dlg.cpp FunctionFix\CspData.cpp FunctionFix\CspLayerObject.cpp FunctionFix\TimeLapseExport.cpp HookTool.cpp ToneAdjustment\ColorBalance_Hook.cpp ToneAdjustment\HSV_Hook.cpp ToneAdjustment\ToneCurve_Hook.cpp
  CSPHook.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\FunctionFix\CspData.h(9,9): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  (compiling source file 'CSPHook.cpp')
  
  dllmain.cpp
  basic_form.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\DUI\DuiCommon.h(12,10): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  (compiling source file 'DUI/basic_form.cpp')
  
  CatDui.cpp
  CatDuiThread.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\DUI\DuiCommon.h(12,10): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  (compiling source file 'DUI/CatDuiThread.cpp')
  
  TimeLapseExport_Dlg.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\DUI\Dialog\TimeLapseExport_Dlg.h(5,10): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  (compiling source file 'DUI/Dialog/TimeLapseExport_Dlg.cpp')
  
  CspData.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\FunctionFix\CspData.h(9,9): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  (compiling source file 'FunctionFix/CspData.cpp')
  
  CspLayerObject.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\FunctionFix\CspData.h(9,9): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  (compiling source file 'FunctionFix/CspLayerObject.cpp')
  
  TimeLapseExport.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\FunctionFix\CspData.h(9,9): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  (compiling source file 'FunctionFix/TimeLapseExport.cpp')
  
  HookTool.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\HookTool.cpp(5,9): error C1083: Cannot open include file: 'duilib/third_party/libudis86/udis86.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  ColorBalance_Hook.cpp
  HSV_Hook.cpp
  ToneCurve_Hook.cpp
D:\a\CSPMOD\CSPMOD\CSPMOD_403\ToneAdjustment\ToneCurve_Hook.cpp(152,22): warning C4244: 'initializing': conversion from 'uintptr_t' to 'uint32_t', possible loss of data [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
D:\a\CSPMOD\CSPMOD\CSPMOD_403\ToneAdjustment\ToneCurve_Hook.cpp(211,22): warning C4244: 'initializing': conversion from 'uintptr_t' to 'uint16_t', possible loss of data [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
D:\a\CSPMOD\CSPMOD\CSPMOD_403\ToneAdjustment\ToneCurve_Hook.cpp(215,23): warning C4244: 'initializing': conversion from 'double' to 'uint16_t', possible loss of data [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
D:\a\CSPMOD\CSPMOD\CSPMOD_403\ToneAdjustment\ToneCurve_Hook.cpp(216,23): warning C4244: 'initializing': conversion from 'double' to 'uint16_t', possible loss of data [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
Done Building Project "D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj" (default targets) -- FAILED.
Project "D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (1) is building "D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj" (3) on node 1 (default targets).
PrepareForBuild:
  Creating directory "x64\Release\".
  Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
  Creating directory "x64\Release\PE_PatchTool.tlog\".
InitializeBuildStatus:
  Creating "x64\Release\PE_PatchTool.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
  Touching "x64\Release\PE_PatchTool.tlog\unsuccessfulbuild".
VcpkgTripletSelection:
  Using triplet "x64-windows" from "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64\CL.exe /c /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /Zi /nologo /W3 /WX- /diagnostics:column /sdl /O2 /Oi /GL /D NDEBUG /D _CONSOLE /D _UNICODE /D UNICODE /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /permissive- /Fo"x64\Release\\" /Fd"x64\Release\vc143.pdb" /external:W3 /Gd /TP /FC /errorReport:queue PE_PatchTool.cpp
  PE_PatchTool.cpp
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u65E0' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u6CD5' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u6253' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u5F00' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u8F93' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u5165' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u6587' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u4EF6' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u65E0' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u6CD5' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u4FDD' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u5B58' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u6587' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u4EF6' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u6587' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u4EF6' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u4FEE' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u6539' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u6210' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u529F' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
Link:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64\link.exe /ERRORREPORT:QUEUE /OUT:"D:\a\CSPMOD\CSPMOD\x64\Release\PE_PatchTool.exe" /NOLOGO /LIBPATH:"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib" /LIBPATH:"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:\a\CSPMOD\CSPMOD\x64\Release\PE_PatchTool.pdb" /SUBSYSTEM:CONSOLE /OPT:REF /OPT:ICF /LTCG:incremental /LTCGOUT:"x64\Release\PE_PatchTool.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\a\CSPMOD\CSPMOD\x64\Release\PE_PatchTool.lib" /MACHINE:X64 x64\Release\PE_PatchTool.obj
  Generating code
  Previous IPDB not found, fall back to full compilation.
  All 202 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  PE_PatchTool.vcxproj -> D:\a\CSPMOD\CSPMOD\x64\Release\PE_PatchTool.exe
AppLocalFromInstalled:
  pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\a\CSPMOD\CSPMOD\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\a\CSPMOD\CSPMOD\x64\Release\PE_PatchTool.exe" "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\bin" "x64\Release\PE_PatchTool.tlog\PE_PatchTool.write.1u.tlog" "x64\Release\vcpkg.applocal.log"
FinalizeBuildStatus:
  Deleting file "x64\Release\PE_PatchTool.tlog\unsuccessfulbuild".
  Touching "x64\Release\PE_PatchTool.tlog\PE_PatchTool.lastbuildstate".
Done Building Project "D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj" (default targets).
Project "D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (1) is building "D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj" (4) on node 1 (default targets).
PrepareForBuild:
  Creating directory ".\..\..\Object\CSPMOD\HSV\Release\x64\".
  Creating directory ".\..\..\OutputWin\CSPMOD\HSV\Release\x64\".
  Creating directory ".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.tlog\".
InitializeBuildStatus:
  Creating ".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
  Touching ".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.tlog\unsuccessfulbuild".
VcpkgTripletSelection:
  Using triplet "x64-windows" from "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.29.30133\bin\HostX86\x64\CL.exe /c /I..\..\Source /I..\..\Resource /I..\..\.. /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /Zi /nologo /W3 /WX- /diagnostics:column /O2 /Oi /GL /D WIN32 /D NDEBUG /D _WINDOWS /D _USRDLL /D HSV_EXPORTS /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MT /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Yc"PlugInCommon\PIFirstHeader.h" /Fp".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.pch" /Fo".\..\..\Object\CSPMOD\HSV\Release\x64\\" /Fd".\..\..\Object\CSPMOD\HSV\Release\x64\vc142.pdb" /external:W3 /Gd /TP /FIPlugInCommon\PIFirstHeader.h /FC /errorReport:queue /utf-8 ..\..\Source\PlugInCommon\PIFirstHeader.cpp
  PIFirstHeader.cpp
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.29.30133\bin\HostX86\x64\CL.exe /c /I..\..\Source /I..\..\Resource /I..\..\.. /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /Zi /nologo /W3 /WX- /diagnostics:column /O2 /Oi /GL /D WIN32 /D NDEBUG /D _WINDOWS /D _USRDLL /D HSV_EXPORTS /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MT /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Yu"PlugInCommon\PIFirstHeader.h" /Fp".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.pch" /Fo".\..\..\Object\CSPMOD\HSV\Release\x64\\" /Fd".\..\..\Object\CSPMOD\HSV\Release\x64\vc142.pdb" /external:W3 /Gd /TP /FIPlugInCommon\PIFirstHeader.h /FC /errorReport:queue /utf-8 ..\..\Source\HSV\PIHSVMain.cpp ..\..\Source\PlugInCommon\Win\PIDLLMainWin.cpp
  PIHSVMain.cpp
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(237,13): warning C4244: 'argument': conversion from 'uint64_t' to 'const TriglavPlugInInt', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(279,132): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(279,121): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(279,117): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(313,140): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(313,129): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(313,125): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(318,161): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(318,150): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(318,146): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(341,9): warning C4244: 'argument': conversion from 'uint64_t' to 'const TriglavPlugInInt', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(145,25): warning C4101: 'processResult': unreferenced local variable [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  PIDLLMainWin.cpp
ResourceCompile:
  C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x86\rc.exe /D _UNICODE /D UNICODE /l"0x0409" /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /nologo /fo".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.res" ..\..\ResourceWin\HSV\HSV.rc 
Link:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.29.30133\bin\HostX86\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\..\..\OutputWin\CSPMOD\HSV\Release\x64\HSV.cpm" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib" /LIBPATH:"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:".\..\..\OutputWin\CSPMOD\HSV\Release\x64\HSV.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG:incremental /LTCGOUT:".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\..\..\OutputWin\CSPMOD\HSV\Release\x64\HSV.lib" /MACHINE:X64 /DLL .\..\..\Object\CSPMOD\HSV\Release\x64\HSV.res
  .\..\..\Object\CSPMOD\HSV\Release\x64\PIHSVMain.obj
  .\..\..\Object\CSPMOD\HSV\Release\x64\PIFirstHeader.obj
  .\..\..\Object\CSPMOD\HSV\Release\x64\PIDLLMainWin.obj
     Creating library .\..\..\OutputWin\CSPMOD\HSV\Release\x64\HSV.lib and object .\..\..\OutputWin\CSPMOD\HSV\Release\x64\HSV.exp
  Generating code
  Previous IPDB not found, fall back to full compilation.
D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(351): warning C4715: 'UpdateView': not all control paths return a value [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  All 376 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  HSV.vcxproj -> D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\OutputWin\CSPMOD\HSV\Release\x64\HSV.cpm
AppLocalFromInstalled:
  pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\a\CSPMOD\CSPMOD\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\OutputWin\CSPMOD\HSV\Release\x64\HSV.cpm" "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\bin" ".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.tlog\HSV.write.1u.tlog" ".\..\..\Object\CSPMOD\HSV\Release\x64\vcpkg.applocal.log"
FinalizeBuildStatus:
  Deleting file ".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.tlog\unsuccessfulbuild".
  Touching ".\..\..\Object\CSPMOD\HSV\Release\x64\HSV.tlog\HSV.lastbuildstate".
Done Building Project "D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj" (default targets).
Project "D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (1) is building "D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj" (5) on node 1 (default targets).
GetCppWinRTProjectWinMDReferences:
  CppWinRTStaticProjectWinMDReferences: 
  CppWinRTDynamicProjectWinMDReferences: 
CppWinRTGetResolvedWinMD:
  GetResolvedWinMD: 
PrepareForBuild:
  Creating directory "x64\Release\".
  Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
  Creating directory "x64\Release\SmoothFi.21f0b160.tlog\".
  Creating directory "x64\Release\Generated Files\".
InitializeBuildStatus:
  Creating "x64\Release\SmoothFi.21f0b160.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
  Touching "x64\Release\SmoothFi.21f0b160.tlog\unsuccessfulbuild".
FxCompile:
  C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\fxc.exe /nologo /E"main" /T cs_5_0 /Fh "Shader\GaussBlurCSHor.h" /Fo "D:\a\CSPMOD\CSPMOD\x64\Release\GaussBlurCSHor.cso" /Vn "GaussBlurCSHor" Graphic\GaussBlurCSHor.hlsl 
  compilation header save succeeded; see D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Shader\GaussBlurCSHor.h
  compilation object save succeeded; see D:\a\CSPMOD\CSPMOD\x64\Release\GaussBlurCSHor.cso
  C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\fxc.exe /nologo /E"main" /T cs_5_0 /Fh "Shader\GaussBlurCSVer.h" /Fo "D:\a\CSPMOD\CSPMOD\x64\Release\GaussBlurCSVer.cso" /Vn "GaussBlurCSVer" Graphic\GaussBlurCSVer.hlsl 
  compilation header save succeeded; see D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Shader\GaussBlurCSVer.h
  compilation object save succeeded; see D:\a\CSPMOD\CSPMOD\x64\Release\GaussBlurCSVer.cso
  C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\fxc.exe /nologo /E"main" /T vs_4_0_level_9_3 /Fh "Shader\VertexShader.h" /Fo "D:\a\CSPMOD\CSPMOD\x64\Release\VertexShader.cso" /Vn "VertexShader" Graphic\VertexShader.hlsl 
  compilation header save succeeded; see D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Shader\VertexShader.h
  compilation object save succeeded; see D:\a\CSPMOD\CSPMOD\x64\Release\VertexShader.cso
GetCppWinRTPlatformWinMDReferences:
  CppWinRTPlatformWinMDReferences: C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.Actions.ActionsContract\*******\Windows.AI.Actions.ActionsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.MachineLearningContract\*******\Windows.AI.MachineLearning.MachineLearningContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract\*******\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.ModelContextProtocol.ModelContextProtocolContract\*******\Windows.AI.ModelContextProtocol.ModelContextProtocolContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract\*******\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ContactActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract\*******\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract\*******\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract\*******\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsPhoneContract\*******\Windows.ApplicationModel.Calls.CallsPhoneContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsVoipContract\*******\Windows.ApplicationModel.Calls.CallsVoipContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.LockScreenCallContract\*******\Windows.ApplicationModel.Calls.LockScreenCallContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract\*******\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.FullTrustAppContract\*******\Windows.ApplicationModel.FullTrustAppContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract\*******\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract\*******\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract\*******\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.Core.SearchCoreContract\*******\Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.SearchContract\*******\Windows.ApplicationModel.Search.SearchContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.SocialInfo.SocialInfoContract\*******\Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.StartupTaskContract\3.0.0.0\Windows.ApplicationModel.StartupTaskContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Wallet.WalletContract\*******\Windows.ApplicationModel.Wallet.WalletContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Custom.CustomDeviceContract\*******\Windows.Devices.Custom.CustomDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.DevicesLowLevelContract\3.0.0.0\Windows.Devices.DevicesLowLevelContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Portable.PortableDeviceContract\*******\Windows.Devices.Portable.PortableDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Power.PowerGridApiContract\*******\Windows.Devices.Power.PowerGridApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.Extensions.ExtensionsContract\*******\Windows.Devices.Printers.Extensions.ExtensionsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.PrintersContract\*******\Windows.Devices.Printers.PrintersContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Scanners.ScannerDeviceContract\*******\Windows.Devices.Scanners.ScannerDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract\3.0.0.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardEmulatorContract\6.0.0.0\Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Sms.LegacySmsApiContract\*******\Windows.Devices.Sms.LegacySmsApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.FoundationContract\*******\Windows.Foundation.FoundationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.UniversalApiContract\********\Windows.Foundation.UniversalApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Input.GamingInputPreviewContract\*******\Windows.Gaming.Input.GamingInputPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Preview.GamesEnumerationContract\*******\Windows.Gaming.Preview.GamesEnumerationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GameChatOverlayContract\*******\Windows.Gaming.UI.GameChatOverlayContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GamingUIProviderContract\*******\Windows.Gaming.UI.GamingUIProviderContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.XboxLive.StorageApiContract\*******\Windows.Gaming.XboxLive.StorageApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract\*******\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Graphics.Printing3D.Printing3DContract\*******\Windows.Graphics.Printing3D.Printing3DContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.Preview.DeploymentPreviewContract\*******\Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.SharedPackageContainerContract\*******\Windows.Management.Deployment.SharedPackageContainerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Update.WindowsUpdateContract\*******\Windows.Management.Update.WindowsUpdateContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Workplace.WorkplaceSettingsContract\*******\Windows.Management.Workplace.WorkplaceSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppBroadcasting.AppBroadcastingContract\*******\Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppRecording.AppRecordingContract\*******\Windows.Media.AppRecording.AppRecordingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppBroadcastContract\*******\Windows.Media.Capture.AppBroadcastContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureContract\*******\Windows.Media.Capture.AppCaptureContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureMetadataContract\*******\Windows.Media.Capture.AppCaptureMetadataContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.CameraCaptureUIContract\*******\Windows.Media.Capture.CameraCaptureUIContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.GameBarContract\*******\Windows.Media.Capture.GameBarContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Devices.CallControlContract\*******\Windows.Media.Devices.CallControlContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.MediaControlContract\*******\Windows.Media.MediaControlContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Playlists.PlaylistsContract\*******\Windows.Media.Playlists.PlaylistsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Protection.ProtectionRenewalContract\*******\Windows.Media.Protection.ProtectionRenewalContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Connectivity.WwanContract\3.0.0.0\Windows.Networking.Connectivity.WwanContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract\*******\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Sockets.ControlChannelTriggerContract\3.0.0.0\Windows.Networking.Sockets.ControlChannelTriggerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract\*******\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract\*******\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.EnterpriseData.EnterpriseDataContract\*******\Windows.Security.EnterpriseData.EnterpriseDataContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.ExchangeActiveSyncProvisioning.EasContract\*******\Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract\*******\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.GuidanceContract\3.0.0.0\Windows.Services.Maps.GuidanceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.LocalSearchContract\*******\Windows.Services.Maps.LocalSearchContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Store.StoreContract\*******\Windows.Services.Store.StoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.TargetedContent.TargetedContentContract\*******\Windows.Services.TargetedContent.TargetedContentContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Storage.Provider.CloudFilesContract\*******\Windows.Storage.Provider.CloudFilesContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\windows.system.profile.platformautomaticappsignincontract\*******\windows.system.profile.platformautomaticappsignincontract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileHardwareTokenContract\*******\Windows.System.Profile.ProfileHardwareTokenContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileRetailInfoContract\*******\Windows.System.Profile.ProfileRetailInfoContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileSharedModeContract\*******\Windows.System.Profile.ProfileSharedModeContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract\3.0.0.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.SystemManagementContract\*******\Windows.System.SystemManagementContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileContract\*******\Windows.System.UserProfile.UserProfileContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileLockScreenContract\*******\Windows.System.UserProfile.UserProfileLockScreenContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ApplicationSettings.ApplicationsSettingsContract\*******\Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract\*******\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.CoreWindowDialogsContract\*******\Windows.UI.Core.CoreWindowDialogsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.SecurityAppManagerContract\*******\Windows.UI.Shell.SecurityAppManagerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.WindowTabManagerContract\*******\Windows.UI.Shell.WindowTabManagerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.UIAutomation.UIAutomationContract\*******\Windows.UI.UIAutomation.UIAutomationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ViewManagement.ViewManagementViewScalingContract\*******\Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Core.Direct.XamlDirectContract\*******\Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Hosting.HostingContract\*******\Windows.UI.Xaml.Hosting.HostingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract\*******\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd
GetCppWinRTDirectWinMDReferences:
  CppWinRTDirectWinMDReferences: 
CppWinRTSetMidlReferences:
  CppWinRTMidlReferences: C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.Actions.ActionsContract\*******\Windows.AI.Actions.ActionsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.MachineLearningContract\*******\Windows.AI.MachineLearning.MachineLearningContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract\*******\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.ModelContextProtocol.ModelContextProtocolContract\*******\Windows.AI.ModelContextProtocol.ModelContextProtocolContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract\*******\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ContactActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract\*******\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract\*******\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract\*******\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsPhoneContract\*******\Windows.ApplicationModel.Calls.CallsPhoneContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsVoipContract\*******\Windows.ApplicationModel.Calls.CallsVoipContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.LockScreenCallContract\*******\Windows.ApplicationModel.Calls.LockScreenCallContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract\*******\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.FullTrustAppContract\*******\Windows.ApplicationModel.FullTrustAppContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract\*******\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract\*******\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract\*******\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.Core.SearchCoreContract\*******\Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.SearchContract\*******\Windows.ApplicationModel.Search.SearchContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.SocialInfo.SocialInfoContract\*******\Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.StartupTaskContract\3.0.0.0\Windows.ApplicationModel.StartupTaskContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Wallet.WalletContract\*******\Windows.ApplicationModel.Wallet.WalletContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Custom.CustomDeviceContract\*******\Windows.Devices.Custom.CustomDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.DevicesLowLevelContract\3.0.0.0\Windows.Devices.DevicesLowLevelContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Portable.PortableDeviceContract\*******\Windows.Devices.Portable.PortableDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Power.PowerGridApiContract\*******\Windows.Devices.Power.PowerGridApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.Extensions.ExtensionsContract\*******\Windows.Devices.Printers.Extensions.ExtensionsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.PrintersContract\*******\Windows.Devices.Printers.PrintersContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Scanners.ScannerDeviceContract\*******\Windows.Devices.Scanners.ScannerDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract\3.0.0.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardEmulatorContract\6.0.0.0\Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Sms.LegacySmsApiContract\*******\Windows.Devices.Sms.LegacySmsApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.FoundationContract\*******\Windows.Foundation.FoundationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.UniversalApiContract\********\Windows.Foundation.UniversalApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Input.GamingInputPreviewContract\*******\Windows.Gaming.Input.GamingInputPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Preview.GamesEnumerationContract\*******\Windows.Gaming.Preview.GamesEnumerationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GameChatOverlayContract\*******\Windows.Gaming.UI.GameChatOverlayContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GamingUIProviderContract\*******\Windows.Gaming.UI.GamingUIProviderContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.XboxLive.StorageApiContract\*******\Windows.Gaming.XboxLive.StorageApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract\*******\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Graphics.Printing3D.Printing3DContract\*******\Windows.Graphics.Printing3D.Printing3DContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.Preview.DeploymentPreviewContract\*******\Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.SharedPackageContainerContract\*******\Windows.Management.Deployment.SharedPackageContainerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Update.WindowsUpdateContract\*******\Windows.Management.Update.WindowsUpdateContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Workplace.WorkplaceSettingsContract\*******\Windows.Management.Workplace.WorkplaceSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppBroadcasting.AppBroadcastingContract\*******\Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppRecording.AppRecordingContract\*******\Windows.Media.AppRecording.AppRecordingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppBroadcastContract\*******\Windows.Media.Capture.AppBroadcastContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureContract\*******\Windows.Media.Capture.AppCaptureContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureMetadataContract\*******\Windows.Media.Capture.AppCaptureMetadataContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.CameraCaptureUIContract\*******\Windows.Media.Capture.CameraCaptureUIContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.GameBarContract\*******\Windows.Media.Capture.GameBarContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Devices.CallControlContract\*******\Windows.Media.Devices.CallControlContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.MediaControlContract\*******\Windows.Media.MediaControlContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Playlists.PlaylistsContract\*******\Windows.Media.Playlists.PlaylistsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Protection.ProtectionRenewalContract\*******\Windows.Media.Protection.ProtectionRenewalContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Connectivity.WwanContract\3.0.0.0\Windows.Networking.Connectivity.WwanContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract\*******\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Sockets.ControlChannelTriggerContract\3.0.0.0\Windows.Networking.Sockets.ControlChannelTriggerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract\*******\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract\*******\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.EnterpriseData.EnterpriseDataContract\*******\Windows.Security.EnterpriseData.EnterpriseDataContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.ExchangeActiveSyncProvisioning.EasContract\*******\Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract\*******\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.GuidanceContract\3.0.0.0\Windows.Services.Maps.GuidanceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.LocalSearchContract\*******\Windows.Services.Maps.LocalSearchContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Store.StoreContract\*******\Windows.Services.Store.StoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.TargetedContent.TargetedContentContract\*******\Windows.Services.TargetedContent.TargetedContentContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Storage.Provider.CloudFilesContract\*******\Windows.Storage.Provider.CloudFilesContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\windows.system.profile.platformautomaticappsignincontract\*******\windows.system.profile.platformautomaticappsignincontract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileHardwareTokenContract\*******\Windows.System.Profile.ProfileHardwareTokenContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileRetailInfoContract\*******\Windows.System.Profile.ProfileRetailInfoContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileSharedModeContract\*******\Windows.System.Profile.ProfileSharedModeContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract\3.0.0.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.SystemManagementContract\*******\Windows.System.SystemManagementContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileContract\*******\Windows.System.UserProfile.UserProfileContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileLockScreenContract\*******\Windows.System.UserProfile.UserProfileLockScreenContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ApplicationSettings.ApplicationsSettingsContract\*******\Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract\*******\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.CoreWindowDialogsContract\*******\Windows.UI.Core.CoreWindowDialogsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.SecurityAppManagerContract\*******\Windows.UI.Shell.SecurityAppManagerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.WindowTabManagerContract\*******\Windows.UI.Shell.WindowTabManagerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.UIAutomation.UIAutomationContract\*******\Windows.UI.UIAutomation.UIAutomationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ViewManagement.ViewManagementViewScalingContract\*******\Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Core.Direct.XamlDirectContract\*******\Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Hosting.HostingContract\*******\Windows.UI.Xaml.Hosting.HostingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract\*******\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd
GetCppWinRTMdMergeInputs:
  CppWinRTMdMergeInputs: 
  CppWinRTMdMergeMetadataDirectories: C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.Actions.ActionsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.MachineLearningContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.ModelContextProtocol.ModelContextProtocolContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivatedEventsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ContactActivatedEventsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsPhoneContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsVoipContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.LockScreenCallContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.FullTrustAppContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.Core.SearchCoreContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.SearchContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.SocialInfo.SocialInfoContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.StartupTaskContract\3.0.0.0\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Wallet.WalletContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Custom.CustomDeviceContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.DevicesLowLevelContract\3.0.0.0\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Portable.PortableDeviceContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Power.PowerGridApiContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.Extensions.ExtensionsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.PrintersContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Scanners.ScannerDeviceContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract\3.0.0.0\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardEmulatorContract\6.0.0.0\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Sms.LegacySmsApiContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.FoundationContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.UniversalApiContract\********\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Input.GamingInputPreviewContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Preview.GamesEnumerationContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GameChatOverlayContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GamingUIProviderContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.XboxLive.StorageApiContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Graphics.Printing3D.Printing3DContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.Preview.DeploymentPreviewContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.SharedPackageContainerContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Update.WindowsUpdateContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Workplace.WorkplaceSettingsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppBroadcasting.AppBroadcastingContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppRecording.AppRecordingContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppBroadcastContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureMetadataContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.CameraCaptureUIContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.GameBarContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Devices.CallControlContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.MediaControlContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Playlists.PlaylistsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Protection.ProtectionRenewalContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Connectivity.WwanContract\3.0.0.0\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Sockets.ControlChannelTriggerContract\3.0.0.0\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.EnterpriseData.EnterpriseDataContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.ExchangeActiveSyncProvisioning.EasContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.GuidanceContract\3.0.0.0\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.LocalSearchContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Store.StoreContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.TargetedContent.TargetedContentContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Storage.Provider.CloudFilesContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\windows.system.profile.platformautomaticappsignincontract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileHardwareTokenContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileRetailInfoContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileSharedModeContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract\3.0.0.0\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.SystemManagementContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileLockScreenContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ApplicationSettings.ApplicationsSettingsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.CoreWindowDialogsContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.SecurityAppManagerContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.WindowTabManagerContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.UIAutomation.UIAutomationContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ViewManagement.ViewManagementViewScalingContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Core.Direct.XamlDirectContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Hosting.HostingContract\*******\;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract\*******\
CppWinRTMergeProjectWinMDInputs:
  Creating directory "x64\Release\Unmerged\".
  Creating directory "x64\Release\Merged\".
  CppWinRTMdMerge output: 
GetCppWinRTPlatformWinMDInputs:
  CppWinRTPlatformWinMDInputs: C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.Actions.ActionsContract\*******\Windows.AI.Actions.ActionsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.MachineLearningContract\*******\Windows.AI.MachineLearning.MachineLearningContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract\*******\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.ModelContextProtocol.ModelContextProtocolContract\*******\Windows.AI.ModelContextProtocol.ModelContextProtocolContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract\*******\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ContactActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract\*******\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract\*******\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract\*******\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsPhoneContract\*******\Windows.ApplicationModel.Calls.CallsPhoneContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsVoipContract\*******\Windows.ApplicationModel.Calls.CallsVoipContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.LockScreenCallContract\*******\Windows.ApplicationModel.Calls.LockScreenCallContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract\*******\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.FullTrustAppContract\*******\Windows.ApplicationModel.FullTrustAppContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract\*******\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract\*******\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract\*******\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.Core.SearchCoreContract\*******\Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.SearchContract\*******\Windows.ApplicationModel.Search.SearchContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.SocialInfo.SocialInfoContract\*******\Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.StartupTaskContract\3.0.0.0\Windows.ApplicationModel.StartupTaskContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Wallet.WalletContract\*******\Windows.ApplicationModel.Wallet.WalletContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Custom.CustomDeviceContract\*******\Windows.Devices.Custom.CustomDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.DevicesLowLevelContract\3.0.0.0\Windows.Devices.DevicesLowLevelContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Portable.PortableDeviceContract\*******\Windows.Devices.Portable.PortableDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Power.PowerGridApiContract\*******\Windows.Devices.Power.PowerGridApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.Extensions.ExtensionsContract\*******\Windows.Devices.Printers.Extensions.ExtensionsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.PrintersContract\*******\Windows.Devices.Printers.PrintersContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Scanners.ScannerDeviceContract\*******\Windows.Devices.Scanners.ScannerDeviceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract\3.0.0.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardEmulatorContract\6.0.0.0\Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Sms.LegacySmsApiContract\*******\Windows.Devices.Sms.LegacySmsApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.FoundationContract\*******\Windows.Foundation.FoundationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.UniversalApiContract\********\Windows.Foundation.UniversalApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Input.GamingInputPreviewContract\*******\Windows.Gaming.Input.GamingInputPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Preview.GamesEnumerationContract\*******\Windows.Gaming.Preview.GamesEnumerationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GameChatOverlayContract\*******\Windows.Gaming.UI.GameChatOverlayContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GamingUIProviderContract\*******\Windows.Gaming.UI.GamingUIProviderContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.XboxLive.StorageApiContract\*******\Windows.Gaming.XboxLive.StorageApiContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract\*******\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Graphics.Printing3D.Printing3DContract\*******\Windows.Graphics.Printing3D.Printing3DContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.Preview.DeploymentPreviewContract\*******\Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.SharedPackageContainerContract\*******\Windows.Management.Deployment.SharedPackageContainerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Update.WindowsUpdateContract\*******\Windows.Management.Update.WindowsUpdateContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Workplace.WorkplaceSettingsContract\*******\Windows.Management.Workplace.WorkplaceSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppBroadcasting.AppBroadcastingContract\*******\Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppRecording.AppRecordingContract\*******\Windows.Media.AppRecording.AppRecordingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppBroadcastContract\*******\Windows.Media.Capture.AppBroadcastContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureContract\*******\Windows.Media.Capture.AppCaptureContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureMetadataContract\*******\Windows.Media.Capture.AppCaptureMetadataContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.CameraCaptureUIContract\*******\Windows.Media.Capture.CameraCaptureUIContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.GameBarContract\*******\Windows.Media.Capture.GameBarContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Devices.CallControlContract\*******\Windows.Media.Devices.CallControlContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.MediaControlContract\*******\Windows.Media.MediaControlContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Playlists.PlaylistsContract\*******\Windows.Media.Playlists.PlaylistsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Protection.ProtectionRenewalContract\*******\Windows.Media.Protection.ProtectionRenewalContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Connectivity.WwanContract\3.0.0.0\Windows.Networking.Connectivity.WwanContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract\*******\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Sockets.ControlChannelTriggerContract\3.0.0.0\Windows.Networking.Sockets.ControlChannelTriggerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract\*******\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract\*******\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.EnterpriseData.EnterpriseDataContract\*******\Windows.Security.EnterpriseData.EnterpriseDataContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.ExchangeActiveSyncProvisioning.EasContract\*******\Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract\*******\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.GuidanceContract\3.0.0.0\Windows.Services.Maps.GuidanceContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.LocalSearchContract\*******\Windows.Services.Maps.LocalSearchContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Store.StoreContract\*******\Windows.Services.Store.StoreContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.TargetedContent.TargetedContentContract\*******\Windows.Services.TargetedContent.TargetedContentContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Storage.Provider.CloudFilesContract\*******\Windows.Storage.Provider.CloudFilesContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\windows.system.profile.platformautomaticappsignincontract\*******\windows.system.profile.platformautomaticappsignincontract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileHardwareTokenContract\*******\Windows.System.Profile.ProfileHardwareTokenContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileRetailInfoContract\*******\Windows.System.Profile.ProfileRetailInfoContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileSharedModeContract\*******\Windows.System.Profile.ProfileSharedModeContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract\3.0.0.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.SystemManagementContract\*******\Windows.System.SystemManagementContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileContract\*******\Windows.System.UserProfile.UserProfileContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileLockScreenContract\*******\Windows.System.UserProfile.UserProfileLockScreenContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ApplicationSettings.ApplicationsSettingsContract\*******\Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract\*******\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.CoreWindowDialogsContract\*******\Windows.UI.Core.CoreWindowDialogsContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.SecurityAppManagerContract\*******\Windows.UI.Shell.SecurityAppManagerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.WindowTabManagerContract\*******\Windows.UI.Shell.WindowTabManagerContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.UIAutomation.UIAutomationContract\*******\Windows.UI.UIAutomation.UIAutomationContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ViewManagement.ViewManagementViewScalingContract\*******\Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Core.Direct.XamlDirectContract\*******\Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Hosting.HostingContract\*******\Windows.UI.Xaml.Hosting.HostingContract.winmd;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract\*******\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd
CppWinRTMakePlatformProjection:
  "D:\a\CSPMOD\CSPMOD\packages\Microsoft.Windows.CppWinRT.2.0.210806.1\build\native\..\..\bin\"cppwinrt @"x64\Release\SmoothFilter_GaussBlur.vcxproj.cppwinrt_plat.rsp"
  "D:\a\CSPMOD\CSPMOD\packages\Microsoft.Windows.CppWinRT.2.0.210806.1\build\native\..\..\bin\"cppwinrt @"x64\Release\SmoothFilter_GaussBlur.vcxproj.cppwinrt_plat.rsp"
VcpkgTripletSelection:
  Using triplet "x64-windows" from "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\CL.exe /c /I..\FilterPlugIn /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /I"x64\Release\Generated Files\\" /Zi /nologo /W3 /WX- /diagnostics:column /sdl /O2 /Oi /GL /D NDEBUG /D SMOOTHFILTERGAUSSBLUR_EXPORTS /D _WINDOWS /D _USRDLL /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /permissive- /Yc"pch.h" /Fp"x64\Release\SmoothFilter_GaussBlur.pch" /Fo"x64\Release\\" /Fd"x64\Release\vc143.pdb" /external:W3 /Gd /TP /FC /errorReport:queue  /bigobj /await pch.cpp
  pch.cpp
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\CL.exe /c /I..\FilterPlugIn /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /I"x64\Release\Generated Files\\" /Zi /nologo /W3 /WX- /diagnostics:column /sdl /O2 /Oi /GL /D NDEBUG /D SMOOTHFILTERGAUSSBLUR_EXPORTS /D _WINDOWS /D _USRDLL /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /permissive- /Fo"x64\Release\\" /Fd"x64\Release\vc143.pdb" /external:W3 /Gd /TP /FC /errorReport:queue  /bigobj /await dllmain.cpp GaussBlur.cpp GaussBlurMain.cpp Graphic\D3D11Graphic.cpp Graphic\GaussBlurContext.cpp Graphic\Texture.cpp
  dllmain.cpp
  GaussBlur.cpp
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\GaussBlur.cpp(185,12): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  GaussBlurMain.cpp
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\GaussBlurMain.cpp(262,36): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D3D11Graphic.cpp
  GaussBlurContext.cpp
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(87,52): warning C4267: '=': conversion from 'size_t' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(170,66): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(170,28): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(186,68): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(186,28): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(209,22): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(239,46): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  Texture.cpp
D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\Texture.cpp(244,21): warning C4018: '<': signed/unsigned mismatch [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\CL.exe /c /I..\FilterPlugIn /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /I"x64\Release\Generated Files\\" /Zi /nologo /W3 /WX- /diagnostics:column /sdl /O2 /Oi /GL /D NDEBUG /D SMOOTHFILTERGAUSSBLUR_EXPORTS /D _WINDOWS /D _USRDLL /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /permissive- /Fo"x64\Release\\" /Fd"x64\Release\vc143.pdb" /external:W3 /Gd /TC /FC /errorReport:queue  /bigobj /await stb_image.c stb_image_write.c
  stb_image.c
  stb_image_write.c
ResourceCompile:
  C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\rc.exe /D _UNICODE /D UNICODE /l"0x0409" /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /nologo /fo"x64\Release\SmoothFilter_GaussBlur.res" SmoothFilter_GaussBlur.rc 
Link:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:"D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_GaussBlur.cpm" /NOLOGO /LIBPATH:"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib" /LIBPATH:"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib\*.lib" WindowsApp.lib /MANIFEST /MANIFESTUAC:NO /manifest:embed /DEBUG /PDB:"D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_GaussBlur.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG:incremental /LTCGOUT:"x64\Release\SmoothFilter_GaussBlur.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_GaussBlur.lib" /MACHINE:X64 /DLL x64\Release\SmoothFilter_GaussBlur.res
  x64\Release\dllmain.obj
  x64\Release\GaussBlur.obj
  x64\Release\GaussBlurMain.obj
  x64\Release\D3D11Graphic.obj
  x64\Release\GaussBlurContext.obj
  x64\Release\Texture.obj
  x64\Release\pch.obj
  x64\Release\stb_image.obj
  x64\Release\stb_image_write.obj
     Creating library D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_GaussBlur.lib and object D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_GaussBlur.exp
  Generating code
  Previous IPDB not found, fall back to full compilation.
  All 170 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  SmoothFilter_GaussBlur.vcxproj -> D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_GaussBlur.cpm
AppLocalFromInstalled:
  pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\a\CSPMOD\CSPMOD\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_GaussBlur.cpm" "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\bin" "x64\Release\SmoothFi.21f0b160.tlog\SmoothFilter_GaussBlur.write.1u.tlog" "x64\Release\vcpkg.applocal.log"
_CopyOutOfDateSourceItemsToOutputDirectory:
Skipping target "_CopyOutOfDateSourceItemsToOutputDirectory" because all output files are up-to-date with respect to the input files.
FinalizeBuildStatus:
  Deleting file "x64\Release\SmoothFi.21f0b160.tlog\unsuccessfulbuild".
  Touching "x64\Release\SmoothFi.21f0b160.tlog\SmoothFilter_GaussBlur.lastbuildstate".
Done Building Project "D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj" (default targets).
Project "D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (1) is building "D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj" (6) on node 1 (default targets).
PrepareForBuild:
  Creating directory "x64\Release\".
  Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
  Creating directory "x64\Release\SmoothFi.48f89e10.tlog\".
InitializeBuildStatus:
  Creating "x64\Release\SmoothFi.48f89e10.tlog\unsuccessfulbuild" because "AlwaysCreate" was specified.
  Touching "x64\Release\SmoothFi.48f89e10.tlog\unsuccessfulbuild".
FxCompile:
  C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x86\fxc.exe /nologo /E"main" /T cs_5_0 /Fh "Shader\MotionBlurCS.h" /Fo "D:\a\CSPMOD\CSPMOD\x64\Release\MotionBlurCS.cso" /Vn "MotionBlurCS" Graphic\MotionBlurCS.hlsl 
  compilation header save succeeded; see D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Shader\MotionBlurCS.h
  compilation object save succeeded; see D:\a\CSPMOD\CSPMOD\x64\Release\MotionBlurCS.cso
VcpkgTripletSelection:
  Using triplet "x64-windows" from "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64\CL.exe /c /I..\FilterPlugIn /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /Zi /nologo /W3 /WX- /diagnostics:column /sdl /O2 /Oi /GL /D NDEBUG /D SMOOTHFILTERMOTIONBLUR_EXPORTS /D _WINDOWS /D _USRDLL /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /permissive- /Yc"pch.h" /Fp"x64\Release\SmoothFilter_MotionBlur.pch" /Fo"x64\Release\\" /Fd"x64\Release\vc143.pdb" /external:W3 /Gd /TP /FC /errorReport:queue pch.cpp
  pch.cpp
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64\CL.exe /c /I..\FilterPlugIn /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /Zi /nologo /W3 /WX- /diagnostics:column /sdl /O2 /Oi /GL /D NDEBUG /D SMOOTHFILTERMOTIONBLUR_EXPORTS /D _WINDOWS /D _USRDLL /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /permissive- /Fo"x64\Release\\" /Fd"x64\Release\vc143.pdb" /external:W3 /Gd /TP /FC /errorReport:queue dllmain.cpp MotionBlur.cpp MotionBlurMain.cpp Graphic\D3D11Graphic.cpp Graphic\MotionBlurContext.cpp Graphic\Texture.cpp
  dllmain.cpp
  MotionBlur.cpp
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\MotionBlur.cpp(196,40): warning C4244: 'argument': conversion from 'TriglavPlugInInt' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\MotionBlur.cpp(196,20): warning C4244: 'argument': conversion from 'TriglavPlugInInt' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  MotionBlurMain.cpp
  D3D11Graphic.cpp
  MotionBlurContext.cpp
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(31,53): warning C4267: '=': conversion from 'size_t' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(112,36): warning C4244: '=': conversion from 'double' to 'int32_t', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(139,66): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(139,28): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(157,18): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(158,18): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(233,30): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(254,30): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  Texture.cpp
D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\Texture.cpp(244,21): warning C4018: '<': signed/unsigned mismatch [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64\CL.exe /c /I..\FilterPlugIn /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /Zi /nologo /W3 /WX- /diagnostics:column /sdl /O2 /Oi /GL /D NDEBUG /D SMOOTHFILTERMOTIONBLUR_EXPORTS /D _WINDOWS /D _USRDLL /D _WINDLL /D _UNICODE /D UNICODE /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /permissive- /Fo"x64\Release\\" /Fd"x64\Release\vc143.pdb" /external:W3 /Gd /TC /FC /errorReport:queue stb_image.c stb_image_write.c
  stb_image.c
  stb_image_write.c
ResourceCompile:
  C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x86\rc.exe /D _UNICODE /D UNICODE /l"0x0409" /I"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\include" /nologo /fo"x64\Release\SmoothFilter_MotionBlur.res" SmoothFilter_MotionBlur.rc 
Link:
  C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64\link.exe /ERRORREPORT:QUEUE /OUT:"D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_MotionBlur.cpm" /NOLOGO /LIBPATH:"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib" /LIBPATH:"D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\lib\*.lib" /MANIFEST /MANIFESTUAC:NO /manifest:embed /DEBUG /PDB:"D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_MotionBlur.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG:incremental /LTCGOUT:"x64\Release\SmoothFilter_MotionBlur.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_MotionBlur.lib" /MACHINE:X64 /DLL x64\Release\SmoothFilter_MotionBlur.res
  x64\Release\dllmain.obj
  x64\Release\MotionBlur.obj
  x64\Release\MotionBlurMain.obj
  x64\Release\D3D11Graphic.obj
  x64\Release\MotionBlurContext.obj
  x64\Release\Texture.obj
  x64\Release\pch.obj
  x64\Release\stb_image.obj
  x64\Release\stb_image_write.obj
     Creating library D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_MotionBlur.lib and object D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_MotionBlur.exp
  Generating code
  Previous IPDB not found, fall back to full compilation.
  All 163 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  SmoothFilter_MotionBlur.vcxproj -> D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_MotionBlur.cpm
AppLocalFromInstalled:
  pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\a\CSPMOD\CSPMOD\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\a\CSPMOD\CSPMOD\x64\Release\SmoothFilter_MotionBlur.cpm" "D:\a\CSPMOD\CSPMOD\vcpkg\installed\x64-windows\bin" "x64\Release\SmoothFi.48f89e10.tlog\SmoothFilter_MotionBlur.write.1u.tlog" "x64\Release\vcpkg.applocal.log"
FinalizeBuildStatus:
  Deleting file "x64\Release\SmoothFi.48f89e10.tlog\unsuccessfulbuild".
  Touching "x64\Release\SmoothFi.48f89e10.tlog\SmoothFilter_MotionBlur.lastbuildstate".
Done Building Project "D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj" (default targets).
Done Building Project "D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (default targets) -- FAILED.

Build FAILED.

"D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (default target) (1) ->
"D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj" (default target) (2) ->
(ClCompile target) -> 
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\ToneAdjustment\ToneCurve_Hook.cpp(152,22): warning C4244: 'initializing': conversion from 'uintptr_t' to 'uint32_t', possible loss of data [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\ToneAdjustment\ToneCurve_Hook.cpp(211,22): warning C4244: 'initializing': conversion from 'uintptr_t' to 'uint16_t', possible loss of data [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\ToneAdjustment\ToneCurve_Hook.cpp(215,23): warning C4244: 'initializing': conversion from 'double' to 'uint16_t', possible loss of data [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\ToneAdjustment\ToneCurve_Hook.cpp(216,23): warning C4244: 'initializing': conversion from 'double' to 'uint16_t', possible loss of data [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]


"D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (default target) (1) ->
"D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj" (default target) (3) ->
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u65E0' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u6CD5' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u6253' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u5F00' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u8F93' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u5165' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u6587' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(220,22): warning C4566: character represented by universal-character-name '\u4EF6' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u65E0' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u6CD5' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u4FDD' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u5B58' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u6587' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(256,22): warning C4566: character represented by universal-character-name '\u4EF6' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u6587' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u4EF6' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u4FEE' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u6539' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u6210' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]
  D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.cpp(261,18): warning C4566: character represented by universal-character-name '\u529F' cannot be represented in the current code page (1252) [D:\a\CSPMOD\CSPMOD\PE_PatchTool\PE_PatchTool.vcxproj]


"D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (default target) (1) ->
"D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj" (default target) (4) ->
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(237,13): warning C4244: 'argument': conversion from 'uint64_t' to 'const TriglavPlugInInt', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(279,132): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(279,121): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(279,117): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(313,140): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(313,129): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(313,125): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(318,161): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(318,150): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(318,146): warning C4244: 'argument': conversion from 'int64_t' to 'const INT32', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(341,9): warning C4244: 'argument': conversion from 'uint64_t' to 'const TriglavPlugInInt', possible loss of data [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(145,25): warning C4101: 'processResult': unreferenced local variable [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]


"D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (default target) (1) ->
"D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj" (default target) (4) ->
(Link target) -> 
  D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\Source\HSV\PIHSVMain.cpp(351): warning C4715: 'UpdateView': not all control paths return a value [D:\a\CSPMOD\CSPMOD\FilterPlugIn\FilterPlugIn\ProjectWin\HSV\HSV.vcxproj]


"D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (default target) (1) ->
"D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj" (default target) (5) ->
(ClCompile target) -> 
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\GaussBlur.cpp(185,12): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\GaussBlurMain.cpp(262,36): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(87,52): warning C4267: '=': conversion from 'size_t' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(170,66): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(170,28): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(186,68): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(186,28): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(209,22): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\GaussBlurContext.cpp(239,46): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\Graphic\Texture.cpp(244,21): warning C4018: '<': signed/unsigned mismatch [D:\a\CSPMOD\CSPMOD\SmoothFilter_GaussBlur\SmoothFilter_GaussBlur.vcxproj]


"D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (default target) (1) ->
"D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj" (default target) (6) ->
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\MotionBlur.cpp(196,40): warning C4244: 'argument': conversion from 'TriglavPlugInInt' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\MotionBlur.cpp(196,20): warning C4244: 'argument': conversion from 'TriglavPlugInInt' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(31,53): warning C4267: '=': conversion from 'size_t' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(112,36): warning C4244: '=': conversion from 'double' to 'int32_t', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(139,66): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(139,28): warning C4244: 'argument': conversion from 'double' to 'UINT', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(157,18): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(158,18): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(233,30): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\MotionBlurContext.cpp(254,30): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]
  D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\Graphic\Texture.cpp(244,21): warning C4018: '<': signed/unsigned mismatch [D:\a\CSPMOD\CSPMOD\SmoothFilter_MotionBlur\SmoothFilter_MotionBlur.vcxproj]


"D:\a\CSPMOD\CSPMOD\CSPMOD.sln" (default target) (1) ->
"D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj" (default target) (2) ->
(ClCompile target) -> 
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\FunctionFix\CspData.h(9,9): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\DUI\DuiCommon.h(12,10): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\DUI\DuiCommon.h(12,10): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\DUI\Dialog\TimeLapseExport_Dlg.h(5,10): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\FunctionFix\CspData.h(9,9): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\FunctionFix\CspData.h(9,9): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\FunctionFix\CspData.h(9,9): error C1083: Cannot open include file: 'duilib/duilib.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]
  D:\a\CSPMOD\CSPMOD\CSPMOD_403\HookTool.cpp(5,9): error C1083: Cannot open include file: 'duilib/third_party/libudis86/udis86.h': No such file or directory [D:\a\CSPMOD\CSPMOD\CSPMOD_403\CSPMOD_403.vcxproj]

    58 Warning(s)
    8 Error(s)

Time Elapsed 00:00:37.66
Error: Process completed with exit code 1.
