name: Build CSPHook Simple

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest
    strategy:
      matrix:
        configuration: [Debug, Release]
        platform: [x64]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v2
    
    - name: Build CSPHook_Simple Project
      run: |
        msbuild CSPHook_Simple.sln /p:Configuration=${{ matrix.configuration }} /p:Platform=${{ matrix.platform }}
      shell: pwsh
    
    - name: Upload Artifacts
      if: success()
      uses: actions/upload-artifact@v4
      with:
        name: CSPHook_Simple-${{ matrix.configuration }}-${{ matrix.platform }}
        path: |
          x64/${{ matrix.configuration }}/CSPHook_Simple.dll
          x64/${{ matrix.configuration }}/CSPHook_Simple.lib
          x64/${{ matrix.configuration }}/CSPHook_Simple.pdb
